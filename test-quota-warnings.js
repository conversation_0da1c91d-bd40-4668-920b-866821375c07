#!/usr/bin/env node

/**
 * Test script to verify quota warning notifications work
 * Run this in development to test the notification system
 */

import { PrismaClient } from '@prisma/client';
import { NotificationService } from './src/backend/services/notifications/notification.service.js';

const prisma = new PrismaClient();

async function testQuotaWarnings() {
  try {
    console.log('🧪 Testing quota warning notifications...');

    // Find a test user (or create one)
    let testUser = await prisma.user.findFirst({
      where: {
        email: { contains: 'test' }
      }
    });

    if (!testUser) {
      console.log('❌ No test user found. Please create a test user first.');
      return;
    }

    console.log(`✅ Found test user: ${testUser.email} (ID: ${testUser.id})`);

    // Test 80% quota warning
    console.log('\n📧 Creating 80% quota warning notification...');
    await NotificationService.createEmailQuotaWarningNotification(
      testUser.id,
      40, // current usage
      50, // limit (free plan default)
      '80%'
    );
    console.log('✅ 80% quota warning created');

    // Test 90% quota warning
    console.log('\n📧 Creating 90% quota warning notification...');
    await NotificationService.createEmailQuotaWarningNotification(
      testUser.id,
      45, // current usage
      50, // limit
      '90%'
    );
    console.log('✅ 90% quota warning created');

    // Test domain verification notification
    console.log('\n🌐 Creating domain verification notification...');
    await NotificationService.createDomainVerifiedNotification(
      testUser.id,
      'test-domain.com'
    );
    console.log('✅ Domain verification notification created');

    // Check notifications were created
    const notifications = await NotificationService.getUserNotifications(testUser.id, { limit: 10 });
    console.log(`\n🔔 User now has ${notifications.length} notifications:`);
    
    notifications.forEach((notif, index) => {
      console.log(`  ${index + 1}. [${notif.type}] ${notif.title}`);
      console.log(`     ${notif.message}`);
      console.log(`     Priority: ${notif.priority}, Category: ${notif.category}`);
      console.log('');
    });

    console.log('🎉 Test completed successfully!');
    console.log('\n💡 Now check your notification bell in the UI to see if they appear.');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testQuotaWarnings();
