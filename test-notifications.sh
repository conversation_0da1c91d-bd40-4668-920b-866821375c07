#!/bin/bash

# Test script for quota warning notifications
# Make sure your development server is running on localhost:3000

echo "🧪 Testing Quota Warning Notifications"
echo "======================================="

# Check if server is running
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Server not running on localhost:3000"
    echo "Please start the development server with: npm run dev"
    exit 1
fi

echo "✅ Server is running"

# You'll need to replace this with your actual auth token
# Get it from browser dev tools -> Application -> Cookies -> user_token
AUTH_TOKEN="your_auth_token_here"

if [ "$AUTH_TOKEN" = "your_auth_token_here" ]; then
    echo ""
    echo "⚠️  Please update the AUTH_TOKEN in this script with your actual token"
    echo "   1. Open your browser dev tools"
    echo "   2. Go to Application -> Cookies"
    echo "   3. Find 'user_token' cookie value"
    echo "   4. Replace 'your_auth_token_here' in this script"
    echo ""
    exit 1
fi

echo ""
echo "📧 Creating 80% quota warning notification..."
curl -X POST http://localhost:3000/api/notifications/test \
  -H "Content-Type: application/json" \
  -H "Cookie: user_token=$AUTH_TOKEN" \
  -d '{
    "title": "Email quota warning (80% used)",
    "message": "You are using 40/50 emails this month (80% of your limit). Consider upgrading or purchasing credits to avoid interruption.",
    "type": "EMAIL_QUOTA_WARNING",
    "category": "BILLING",
    "priority": "MEDIUM"
  }'

echo ""
echo ""
echo "📧 Creating 90% quota warning notification..."
curl -X POST http://localhost:3000/api/notifications/test \
  -H "Content-Type: application/json" \
  -H "Cookie: user_token=$AUTH_TOKEN" \
  -d '{
    "title": "Email quota warning (90% used)",
    "message": "You are using 45/50 emails this month (90% of your limit). Upgrade now to avoid service interruption.",
    "type": "EMAIL_QUOTA_WARNING",
    "category": "BILLING",
    "priority": "HIGH"
  }'

echo ""
echo ""
echo "🌐 Creating domain verification notification..."
curl -X POST http://localhost:3000/api/notifications/test \
  -H "Content-Type: application/json" \
  -H "Cookie: user_token=$AUTH_TOKEN" \
  -d '{
    "title": "Domain verified successfully",
    "message": "Your domain test-domain.com has been verified and is now ready to receive emails.",
    "type": "DOMAIN_VERIFIED",
    "category": "DOMAIN",
    "priority": "MEDIUM"
  }'

echo ""
echo ""
echo "🔔 Fetching your notifications..."
curl -X GET "http://localhost:3000/api/notifications?limit=10" \
  -H "Cookie: user_token=$AUTH_TOKEN" \
  | jq '.notifications[] | {type: .type, title: .title, priority: .priority, createdAt: .createdAt}'

echo ""
echo ""
echo "🎉 Test completed!"
echo "Check your notification bell in the UI to see if the notifications appear."
echo ""
echo "💡 Tips:"
echo "   - Notifications should appear in real-time via WebSocket"
echo "   - Check browser console for any WebSocket errors"
echo "   - The notification bell should show a red badge with count"
