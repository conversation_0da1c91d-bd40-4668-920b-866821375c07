# 🧪 Testing Quota Warning Notifications

## Quick Test Methods

### Method 1: Using the Test API Endpoint (Easiest)

1. **Get your auth token:**
   - Open browser dev tools (F12)
   - Go to Application → Cookies
   - Copy the `user_token` value

2. **Run the test script:**
   ```bash
   # Edit the script first to add your auth token
   nano test-notifications.sh
   # Replace 'your_auth_token_here' with your actual token
   
   # Then run it
   ./test-notifications.sh
   ```

3. **Or use curl directly:**
   ```bash
   # 80% quota warning
   curl -X POST http://localhost:3000/api/notifications/test \
     -H "Content-Type: application/json" \
     -H "Cookie: user_token=YOUR_TOKEN_HERE" \
     -d '{
       "title": "Email quota warning (80% used)",
       "message": "You are using 40/50 emails this month (80% of your limit).",
       "type": "EMAIL_QUOTA_WARNING",
       "category": "BILLING",
       "priority": "MEDIUM"
     }'
   
   # 90% quota warning  
   curl -X POST http://localhost:3000/api/notifications/test \
     -H "Content-Type: application/json" \
     -H "Cookie: user_token=YOUR_TOKEN_HERE" \
     -d '{
       "title": "Email quota warning (90% used)",
       "message": "You are using 45/50 emails this month (90% of your limit).",
       "type": "EMAIL_QUOTA_WARNING",
       "category": "BILLING",
       "priority": "HIGH"
     }'
   ```

### Method 2: Simulate Real Email Usage (More Realistic)

1. **Find your user ID:**
   ```bash
   # In your database console or via API
   curl -H "Cookie: user_token=YOUR_TOKEN" http://localhost:3000/api/user/profile
   ```

2. **Manually set email usage to trigger warnings:**
   ```sql
   -- Connect to your database and run:
   
   -- For 80% warning (40/50 emails for free plan)
   UPDATE users SET "currentMonthEmails" = 40 WHERE id = 'YOUR_USER_ID';
   
   -- For 90% warning (45/50 emails for free plan)  
   UPDATE users SET "currentMonthEmails" = 45 WHERE id = 'YOUR_USER_ID';
   ```

3. **Process a test email to trigger the warning check:**
   - Send an email to your test webhook address
   - Or use the test webhook feature in the UI

### Method 3: Using Browser Console (Quick & Easy)

1. **Open your app in the browser**
2. **Open dev tools console (F12)**
3. **Run this JavaScript:**
   ```javascript
   // Create test notifications directly
   fetch('/api/notifications/test', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({
       title: 'Email quota warning (80% used)',
       message: 'You are using 40/50 emails this month (80% of your limit).',
       type: 'EMAIL_QUOTA_WARNING',
       category: 'BILLING',
       priority: 'MEDIUM'
     })
   }).then(r => r.json()).then(console.log);
   ```

## What to Expect

After creating notifications, you should see:

1. **🔔 Notification Bell**: Red badge with count
2. **📱 Real-time Update**: Notifications appear without page refresh
3. **🎯 Proper Content**: 
   - 80% warning: Medium priority, "View Billing" button
   - 90% warning: High priority, "Upgrade Now" button
4. **🚫 No Duplicates**: Same warning won't appear twice in 24 hours

## Troubleshooting

### Notifications Not Appearing?

1. **Check WebSocket connection:**
   ```javascript
   // In browser console
   console.log('WebSocket events:', window.webSocketEvents || 'Not connected');
   ```

2. **Check API response:**
   ```bash
   curl -H "Cookie: user_token=YOUR_TOKEN" http://localhost:3000/api/notifications
   ```

3. **Check browser console for errors**

4. **Verify notification service is working:**
   ```bash
   # Check server logs for notification creation
   tail -f logs/app.log | grep notification
   ```

### Database Issues?

```bash
# Check if notification table exists
psql -d your_database -c "\dt notification*"

# Check recent notifications
psql -d your_database -c "SELECT type, title, created_at FROM notifications ORDER BY created_at DESC LIMIT 5;"
```

## Expected Behavior in Production

- **80% Usage**: User gets medium priority warning with "View Billing" action
- **90% Usage**: User gets high priority warning with "Upgrade Now" action  
- **100% Usage**: User gets quota exhausted notification and emails are blocked
- **Domain Verified**: User gets success notification when domains are verified
- **No Spam**: Duplicate warnings are prevented by 24-hour cooldown
