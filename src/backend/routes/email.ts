import { FastifyBaseLogger, FastifyPluginAsync } from 'fastify';
import { EmailParser } from '../services/email-parser.js';
import { queueWebhookDelivery } from '../services/queue.js';
import { prisma } from '../lib/prisma.js';
import { incrementUserEmailUsage } from '../lib/auth.js';
import { webSocketService } from '../services/websocket.service.js';
import { PlanLimitMiddleware } from '../middleware/plan-limits.middleware.js';

/**
 * Calculate email expiration date based on user settings and plan defaults
 */
async function calculateEmailExpiration(userId: string, planType: string): Promise<Date> {
  // Get user's retention settings
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      settings: {
        select: { dataRetentionHours: true }
      }
    }
  });

  let retentionHours: number;

  // Use custom setting if available, otherwise use plan default
  if (user?.settings?.dataRetentionHours !== null && user?.settings?.dataRetentionHours !== undefined) {
    retentionHours = user.settings.dataRetentionHours;
  } else {
    // Plan-based defaults
    switch (planType) {
      case 'free':
        retentionHours = 2;
        break;
      case 'pro':
      case 'enterprise':
        retentionHours = 24;
        break;
      default:
        retentionHours = 2; // Default to free plan retention
    }
  }

  return new Date(Date.now() + (retentionHours * 60 * 60 * 1000));
}

// This route will be called by Postfix when an email arrives
export const emailRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' }; // Reference to global schema

  // Endpoint for processing incoming emails (called by mail server)
  // This corresponds to /webhook/email in openapi-spec.ts
  fastify.post('/process', {
    schema: {
      description: 'Endpoint for Postfix (or other MTA) to forward emails to. This typically requires IP whitelisting or a secret token in the URL/header, not standard user API keys.',
      tags: ['Webhook'],
      summary: 'Receive incoming email (via MTA)',
      body: {
        // This reflects the `message/rfc822` part of the OpenAPI spec
        // Fastify handles raw body as string or buffer if content type matches.
        // For Swagger, we describe it as a binary string.
        type: 'string',
        format: 'binary', // Or just 'string' if specific format isn't critical for docs
        description: 'Raw email content (e.g., RFC 822 format)',
      },
      response: {
        202: {
          description: 'Email accepted for processing.',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            messageId: { type: 'string', example: '<<EMAIL>>' },
            status: { type: 'string', example: 'queued' },
            emailId: { type: 'string', example: 'cmbw6hopl002uru1qqyx7b18a' },
          },
        },
        '400': { description: 'Invalid email data or missing data', ...errorResponseSchema },
        '403': { description: 'Domain not verified or inactive', ...errorResponseSchema },
        404: { description: 'Domain not configured or no webhook for address', ...errorResponseSchema },
        500: { description: 'Internal server error processing email', ...errorResponseSchema },
      },
    },
  }, async (request, reply) => {
    try {
      const rawEmail = request.body as string | Buffer;
      
      if (!rawEmail || rawEmail.length === 0) {
        return reply.code(400).send({ statusCode: 400, error: 'Bad Request', message: 'No email data provided' });
      }

      // Extract target domain from the raw email headers first
      const targetDomain = extractDomainFromRawEmail(rawEmail);

      if (!targetDomain) {
        // Enhanced logging for debugging SpamAssassin issues
        const emailText = typeof rawEmail === 'string' ? rawEmail : rawEmail.toString();
        const headerSection = emailText.split('\n\n')[0];
        const relevantHeaders = headerSection.split('\n')
          .filter(line => /^(X-Original-To|Delivered-To|Envelope-To|To):/i.test(line))
          .join('\n');

        // Show ALL headers for comprehensive debugging
        const allHeaders = headerSection.split('\n')
          .filter(line => line.includes(':'))
          .map(line => line.split(':')[0])
          .join(', ');

        fastify.log.warn({
          relevantHeaders,
          allHeaders,
          emailSize: emailText.length,
          isSpamProcessed: emailText.includes('X-Spam-Status'),
          processingScript: request.headers['x-processing-script'],
          firstFewLines: headerSection.split('\n').slice(0, 10).join('\n')
        }, 'No target domain found in email headers');

        return reply.code(400).send({ statusCode: 400, error: 'Bad Request', message: 'Invalid recipient domain in email headers' });
      }

      // Check if this is a test webhook for test.emailconnect.eu BEFORE domain lookup
      if (targetDomain === 'test.emailconnect.eu') {
        // Parse the email to get recipient info
        const parsedEmail = await EmailParser.parseToWebhookPayload(rawEmail, targetDomain);
        const userIdSuffix = parsedEmail.message.recipient.email.split('@')[0];
        const testUser = await findUserByIdSuffix(userIdSuffix);

        if (testUser) {
          // Check user's email quota using plan limit middleware
          const user = await prisma.user.findUnique({
            where: { id: testUser.id },
            select: { planType: true }
          });

          if (!user) {
            return reply.code(404).send({
              statusCode: 404,
              error: 'Not Found',
              message: 'User not found'
            });
          }

          const quotaValidation = await PlanLimitMiddleware.validateEmailQuota(testUser.id, user.planType);
          if (!quotaValidation.allowed) {
            fastify.log.warn({
              domain: targetDomain,
              userId: testUser.id,
              currentUsage: quotaValidation.currentUsage,
              limit: quotaValidation.limit,
              reason: quotaValidation.reason
            }, 'Test webhook email rejected - user exceeded email quota');

            return reply.code(429).send({
              statusCode: 429,
              error: 'Too Many Requests',
              message: quotaValidation.reason,
              currentUsage: quotaValidation.currentUsage,
              monthlyLimit: quotaValidation.limit,
            });
          }

          // Calculate expiration based on user settings for test webhooks too
          const expiresAt = await calculateEmailExpiration('test-user', 'free'); // Use free plan default for test webhooks

          // Store webhook payload directly instead of queuing
          const emailRecord = await prisma.email.create({
            data: {
              messageId: parsedEmail.envelope.messageId,
              fromAddress: parsedEmail.message.sender.email,
              toAddresses: [parsedEmail.message.recipient.email],
              subject: parsedEmail.message.subject,
              domainId: null, // Special case for test webhooks
              webhookPayload: parsedEmail,
              isTestWebhook: true,
              deliveryStatus: 'DELIVERED',
              deliveredAt: new Date(),
              expiresAt,
            },
          });

          // Emit WebSocket event for real-time update
          webSocketService.emitEmailProcessed(testUser.id, {
            messageId: parsedEmail.envelope.messageId,
            fromAddress: parsedEmail.message.sender.email,
            subject: parsedEmail.message.subject || '(no subject)',
            isTestWebhook: true,
            deliveryStatus: 'DELIVERED',
            webhookPayload: parsedEmail,
            timestamp: new Date().toISOString()
          });

          // Note: Test webhooks should NOT increment usage count
          // await incrementUserEmailUsage(testUser.id);

          fastify.log.info({
            messageId: parsedEmail.envelope.messageId,
            userId: testUser.id,
            testEmail: parsedEmail.message.recipient.email
          }, 'Test webhook email processed');

          return reply.code(202).send({
            success: true,
            messageId: parsedEmail.envelope.messageId,
            status: 'delivered',
            emailId: emailRecord.id,
            isTestWebhook: true
          });
        } else {
          // No user found with this suffix
          fastify.log.info({
            domain: targetDomain,
            userIdSuffix,
            email: parsedEmail.message.recipient.email
          }, 'Test webhook email rejected - no user found with suffix');
          return reply.code(404).send({
            statusCode: 404,
            error: 'Not Found',
            message: 'No user found for this test email address'
          });
        }
      }

      // Security check: Only process emails for verified domains
      const domainConfig = await prisma.domain.findUnique({
        where: { domain: targetDomain },
        include: {
          aliases: {
            include: { webhook: true },
            where: { active: true }
          }
        },
      });

      if (!domainConfig) {
        fastify.log.info({ domain: targetDomain }, 'Domain not configured');
        return reply.code(404).send({ statusCode: 404, error: 'Not Found', message: 'Domain not configured' });
      }

      // Critical security check: Domain must be verified to receive emails
      if (!domainConfig.verified ) { // Corrected: Prisma model likely uses 'verified'
        fastify.log.warn({
        domain: targetDomain,
        verified: domainConfig.verified
        }, 'Email rejected - domain not verified');

        return reply.code(403).send({
        statusCode: 403,
        error: 'Forbidden',
        message: 'Domain not verified. Email processing is disabled until domain ownership is verified.',
          verificationStatus: domainConfig.verificationStatus,
        });
      }

      // Check user's email quota using plan limit middleware
      const user = await prisma.user.findUnique({
        where: { id: domainConfig.userId },
        select: { planType: true }
      });

      if (!user) {
        return reply.code(500).send({
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'User data inconsistency'
        });
      }

      const quotaValidation = await PlanLimitMiddleware.validateEmailQuota(domainConfig.userId, user.planType);
      if (!quotaValidation.allowed) {
        fastify.log.warn({
          domain: targetDomain,
          userId: domainConfig.userId,
          currentUsage: quotaValidation.currentUsage,
          limit: quotaValidation.limit,
          reason: quotaValidation.reason
        }, 'Email rejected - user exceeded email quota');
        
        return reply.code(429).send({
          statusCode: 429,
          error: 'Too Many Requests',
          message: quotaValidation.reason,
          currentUsage: quotaValidation.currentUsage,
          monthlyLimit: quotaValidation.limit,
        });
      }

      // Domain must be active
      if (!domainConfig.active) { // Assuming 'active' field exists, as per original logic
        fastify.log.info({ domain: targetDomain }, 'Email rejected - domain inactive');
        return reply.code(403).send({ statusCode: 403, error: 'Forbidden', message: 'Domain is inactive' });
      }

      // Parse the email using the enhanced parser
      const parsedEmail = await EmailParser.parseToWebhookPayload(rawEmail, targetDomain);

      // Look up webhook configuration for this domain/email
      const webhookConfig = await lookupWebhookConfig(domainConfig, parsedEmail.message.recipient.email, fastify.log);

      if (!webhookConfig) {
        fastify.log.info({ domain: targetDomain, messageId: parsedEmail.envelope.messageId }, 'No webhook configured');
        return reply.code(404).send({ statusCode: 404, error: 'Not Found', message: 'No webhook configured for this email address or domain' });
      }

      // Debug logging for configuration
      fastify.log.debug({
        domain: targetDomain,
        messageId: parsedEmail.envelope.messageId,
        webhookConfig: webhookConfig.configuration,
        includeEnvelope: webhookConfig.configuration?.includeEnvelope,
        configType: typeof webhookConfig.configuration?.includeEnvelope
      }, 'Webhook configuration before filtering');

      // Apply configuration filters to the parsed email
      const filteredEmail = applyConfigurationFilters(parsedEmail, webhookConfig.configuration);

      // Debug logging for filtered email
      fastify.log.debug({
        domain: targetDomain,
        messageId: parsedEmail.envelope.messageId,
        hasEnvelopeAfterFilter: !!filteredEmail.envelope,
        originalMessageId: parsedEmail.envelope.messageId,
        filteredMessageId: filteredEmail.envelope?.messageId
      }, 'Email after configuration filtering');

      // Calculate expiration based on user settings and plan type
      const expiresAt = await calculateEmailExpiration(domainConfig.userId, user.planType);

      // Create email record for tracking
      const emailRecord = await prisma.email.create({
        data: {
          messageId: parsedEmail.envelope.messageId,
          fromAddress: parsedEmail.message.sender.email,
          toAddresses: [parsedEmail.message.recipient.email],
          subject: parsedEmail.message.subject,
          domainId: domainConfig.id,
          userId: domainConfig.userId, // Add userId for efficient retention queries
          expiresAt,
          // deliveryStatus defaults to PENDING
        },
      });

      // Create payload for queue with preserved messageId for tracking
      const queuePayload = {
        ...filteredEmail,
        // Always preserve messageId for internal tracking, even if envelope is filtered out
        _internalMessageId: parsedEmail.envelope.messageId
      };

      // Queue for webhook delivery using the payload with preserved messageId
      await queueWebhookDelivery(webhookConfig.url, queuePayload, webhookConfig.secret, webhookConfig.customHeaders);

      // Increment user's email usage count (only after successful processing)
      await incrementUserEmailUsage(domainConfig.userId);

      // Emit WebSocket event for real-time update
      webSocketService.emitEmailProcessed(domainConfig.userId, {
        messageId: parsedEmail.envelope.messageId,
        fromAddress: parsedEmail.message.sender.email,
        subject: parsedEmail.message.subject || '(no subject)',
        isTestWebhook: false,
        deliveryStatus: 'DELIVERED', // Will be updated by queue processor
        timestamp: new Date().toISOString()
      });

      fastify.log.info({
        messageId: parsedEmail.envelope.messageId,
        domain: targetDomain,
        webhookUrl: webhookConfig.url,
        hasSecret: !!webhookConfig.secret,
        emailRecordId: emailRecord.id,
        userId: domainConfig.userId
      }, 'Email processed and queued for webhook delivery');

      // Changed to 202 as per OpenAPI spec
      return reply.code(202).send({
        success: true,
        messageId: parsedEmail.envelope.messageId,
        status: 'queued',
        emailId: emailRecord.id,
      });

    } catch (error: any) {
      fastify.log.error({ error: error.message, stack: error.stack }, 'Failed to process email');
      return reply.code(500).send({ statusCode: 500, error: 'Internal Server Error', message: 'Failed to process email' });
    }
  });

  // Get email processing status
  fastify.get('/status/:messageId', {
    schema: {
      description: 'Get the processing and delivery status of a specific email.',
      tags: ['Webhook Operations'], // A new tag, or could be 'Webhook' or 'Admin'
      summary: 'Get email delivery status',
      params: {
        type: 'object',
        properties: {
          messageId: { type: 'string', description: 'The unique message ID of the email.' },
        },
        required: ['messageId'],
      },
      response: {
        200: {
          description: 'Detailed status of the email.',
          type: 'object',
          properties: {
            messageId: { type: 'string' },
            status: { type: 'string', enum: ['queued', 'sent', 'delivered', 'failed', 'deferred'] }, // Example statuses
            deliveryAttempts: { type: 'integer' },
            lastAttempt: { type: 'string', format: 'date-time', nullable: true },
            deliveredAt: { type: 'string', format: 'date-time', nullable: true },
            errorMessage: { type: 'string', nullable: true },
            domain: { type: 'string' },
            webhookUrl: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            // expiresAt: { type: 'string', format: 'date-time' }, // If still relevant
          },
        },
        404: { description: 'Email not found', ...errorResponseSchema },
        500: { description: 'Failed to retrieve email status', ...errorResponseSchema },
      },
    },
  }, async (request, reply) => {
    const { messageId } = request.params as { messageId: string };
    
    try {
      // Get email status with domain info
      const emailRecord = await prisma.email.findUnique({
        where: { messageId },
        include: {
          domain: {
            select: { domain: true }
          }
        },
      });
      
      if (!emailRecord) {
        return reply.code(404).send({ statusCode: 404, error: 'Not Found', message: 'Email not found' });
      }
      
      // Return email status
      return reply.send({
        messageId,
        status: emailRecord.deliveryStatus.toLowerCase(),
        deliveryAttempts: emailRecord.deliveryAttempts,
        lastAttempt: emailRecord.lastAttemptAt?.toISOString(),
        deliveredAt: emailRecord.deliveredAt?.toISOString(),
        errorMessage: emailRecord.errorMessage,
        domain: emailRecord.domain?.domain,
        createdAt: emailRecord.createdAt.toISOString(),
        expiresAt: emailRecord.expiresAt.toISOString(),
      });
      
    } catch (error: any) {
      fastify.log.error({ messageId, error: error.message, stack: error.stack }, 'Failed to get email status');
      
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve email status',
        // details: error.message, // Optionally include for debugging, but be careful in prod
      });
    }
  });
};

/**
 * Extract domain from raw email headers before full parsing
 * This is more efficient and handles the case where parsing might fail
 * Enhanced to handle SpamAssassin-processed emails
 */
function extractDomainFromRawEmail(rawEmail: string | Buffer): string | null {
  const emailText = typeof rawEmail === 'string' ? rawEmail : rawEmail.toString();

  // Look for common email headers that contain the recipient
  // Order matters: X-Original-To is most reliable for our setup
  // Enhanced patterns to handle SpamAssassin modifications
  const patterns = [
    // Standard X-Original-To header
    /^X-Original-To:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
    // X-Original-To with angle brackets
    /^X-Original-To:\s*<([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})>/im,
    // X-Original-To with extra text
    /^X-Original-To:\s*.*?([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
    // Delivered-To variations
    /^Delivered-To:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
    /^Delivered-To:\s*<([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})>/im,
    /^Delivered-To:\s*.*?([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
    // Envelope-To variations
    /^Envelope-To:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
    /^Envelope-To:\s*<([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})>/im,
    // To header variations
    /^To:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
    /^To:\s*<([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})>/im,
    /^To:\s*.*?([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
  ];

  for (const pattern of patterns) {
    const match = emailText.match(pattern);
    if (match && match[1]) {
      // Extract domain from the full email address
      const emailAddress = match[1];
      const atIndex = emailAddress.indexOf('@');
      if (atIndex !== -1) {
        const domain = emailAddress.substring(atIndex + 1).toLowerCase().trim();
        // Remove any trailing characters that might be present
        const cleanDomain = domain.replace(/[>\s\r\n].*$/, '');
        if (cleanDomain && cleanDomain.includes('.')) {
          return cleanDomain;
        }
      }
    }
  }

  // Enhanced fallback: look for any email address in the headers section
  const headerSection = emailText.split('\n\n')[0]; // Get headers only
  const emailMatches = headerSection.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/gi);

  if (emailMatches) {
    // Look for addresses that are likely recipients (not senders)
    for (const emailAddr of emailMatches) {
      const domain = emailAddr.substring(emailAddr.indexOf('@') + 1).toLowerCase();
      // Skip common sender domains that are unlikely to be our target
      if (!domain.includes('gmail.com') && !domain.includes('outlook.com') &&
          !domain.includes('yahoo.com') && !domain.includes('hotmail.com')) {
        return domain;
      }
    }

    // If no non-common domains found, use the first match
    const firstEmail = emailMatches[0];
    const atIndex = firstEmail.indexOf('@');
    if (atIndex !== -1) {
      return firstEmail.substring(atIndex + 1).toLowerCase();
    }
  }

  return null;
}

/**
 * Look up webhook configuration for domain/email combination
 * Checks for specific alias first, then catch-all alias
 */
async function lookupWebhookConfig(
  domainConfig: any,
  emailAddress: string,
  log: FastifyBaseLogger
): Promise<{ url: string; secret?: string; configuration: any; customHeaders?: Record<string, string> } | null> {
  // 1. Check for specific alias configuration first
  const specificAlias = domainConfig.aliases.find((alias: any) =>
    alias.email === emailAddress && alias.active && alias.webhook
  );

  if (specificAlias && specificAlias.webhook) {
    log.debug({
      emailAddress,
      webhookUrl: specificAlias.webhook.url,
      webhookName: specificAlias.webhook.name,
      hasSecret: !!specificAlias.webhook.webhookSecret
    }, 'Using specific alias webhook');
    return {
      url: specificAlias.webhook.url,
      secret: specificAlias.webhook.webhookSecret || undefined,
      configuration: specificAlias.configuration || {},
      customHeaders: specificAlias.webhook.customHeaders as Record<string, string> || undefined
    };
  }

  // 2. Fall back to catch-all alias webhook
  const catchAllAlias = domainConfig.aliases.find((alias: any) =>
    alias.email.startsWith('*@') && alias.active && alias.webhook
  );

  if (catchAllAlias && catchAllAlias.webhook) {
    log.debug({
      domain: domainConfig.domain,
      emailAddress,
      webhookUrl: catchAllAlias.webhook.url,
      webhookName: catchAllAlias.webhook.name,
      hasSecret: !!catchAllAlias.webhook.webhookSecret
    }, 'Using catch-all alias webhook');
    return {
      url: catchAllAlias.webhook.url,
      secret: catchAllAlias.webhook.webhookSecret || undefined,
      configuration: catchAllAlias.configuration || domainConfig.configuration || {},
      customHeaders: catchAllAlias.webhook.customHeaders as Record<string, string> || undefined
    };
  }

  // 3. No webhook configured
  log.warn({
    domain: domainConfig.domain,
    emailAddress,
    aliasCount: domainConfig.aliases.length,
    hasCatchAll: domainConfig.aliases.some((a: any) => a.email.startsWith('*@'))
  }, 'No webhook configured for email or domain');

  return null;
}

/**
 * Apply configuration filters to email payload
 */
function applyConfigurationFilters(email: any, configuration: any) {
  const filtered = { ...email };

  // Filter attachments if disabled
  if (configuration.allowAttachments === false) {
    filtered.message.attachments = [];
  }

  // Filter envelope data if disabled
  if (configuration.includeEnvelope === false) {
    delete filtered.envelope;
  }

  return filtered;
}

/**
 * Find user by the last 8 characters of their ID
 */
async function findUserByIdSuffix(suffix: string): Promise<{ id: string } | null> {
  if (suffix.length !== 8) {
    return null;
  }

  try {
    // Find user where ID ends with the suffix
    const user = await prisma.user.findFirst({
      where: {
        id: {
          endsWith: suffix
        }
      },
      select: {
        id: true
      }
    });

    return user;
  } catch (error) {
    return null;
  }
}