import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { env } from '../../config/env.js';
import { logger } from '../../utils/logger.js';
import { prisma } from '../../lib/prisma.js';
import { PlanConfigService } from '../billing/plan-config.service.js';
import { UsageCalculationService } from '../billing/usage-calculation.service.js';

// User auth constants
const USER_JWT_SECRET = env.USER_JWT_SECRET;
const USER_JWT_EXPIRES_IN = env.USER_JWT_EXPIRES_IN;

export interface UserRegisterData {
  email: string;
  password: string;
  name?: string;
}

export interface UserLoginData {
  email: string;
  password: string;
}

export interface UserTokenPayload {
  userId: string;
  email: string;
  iat: number;
  exp: number;
}

export interface UserProfile {
  id: string;
  email: string;
  name: string | null;
  planType: string;
  currentMonthEmails: number;
  verified: boolean;
}

export class UserAuthService {
  /**
   * Validate password requirements
   */
  private validatePassword(password: string): string | null {
    if (!password || password.length < 8) {
      return 'Password must be at least 8 characters long';
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!/(?=.*\d)/.test(password)) {
      return 'Password must contain at least one number';
    }
    return null;
  }

  /**
   * Validate email format
   */
  private validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Register a new user
   */
  async registerUser(userData: UserRegisterData): Promise<{ success: boolean; user?: UserProfile; token?: string; error?: string }> {
    if (!userData.email || !userData.password) {
      return { success: false, error: 'Email and password are required' };
    }

    // Validate email format
    if (!this.validateEmail(userData.email)) {
      return { success: false, error: 'Invalid email format' };
    }

    // Validate password
    const passwordError = this.validatePassword(userData.password);
    if (passwordError) {
      return { success: false, error: passwordError };
    }

    try {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email }
      });

      if (existingUser) {
        return { success: false, error: 'User with this email already exists' };
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      // Create user
      const user = await prisma.user.create({
        data: {
          email: userData.email,
          password: hashedPassword,
          name: userData.name || null,
          verified: true, // No email verification required
        },
        select: {
          id: true,
          email: true,
          name: true,
          planType: true,
          currentMonthEmails: true,
          verified: true,
        }
      });

      // Generate JWT token
      const tokenResult = this.generateToken({ userId: user.id, email: user.email });
      if (!tokenResult.success) {
        return { success: false, error: tokenResult.error };
      }

      return {
        success: true,
        user,
        token: tokenResult.token
      };

    } catch (error) {
      logger.error({ err: error }, 'Error during user registration');
      return { success: false, error: 'Internal server error during registration' };
    }
  }

  /**
   * Authenticate user login
   */
  async loginUser(credentials: UserLoginData): Promise<{ success: boolean; user?: UserProfile; token?: string; error?: string }> {
    if (!credentials.email || !credentials.password) {
      return { success: false, error: 'Email and password are required' };
    }

    try {
      // Find user
      const user = await prisma.user.findUnique({
        where: { email: credentials.email },
        select: {
          id: true,
          email: true,
          name: true,
          password: true,
          planType: true,
          currentMonthEmails: true,
          verified: true,
        }
      });

      if (!user) {
        return { success: false, error: 'Invalid credentials' };
      }

      // Check password
      const passwordMatch = await bcrypt.compare(credentials.password, user.password);
      if (!passwordMatch) {
        return { success: false, error: 'Invalid credentials' };
      }

      // Generate JWT token
      const tokenResult = this.generateToken({ userId: user.id, email: user.email });
      if (!tokenResult.success) {
        return { success: false, error: tokenResult.error };
      }

      // Remove password from user object
      const { password, ...userProfile } = user;

      return {
        success: true,
        user: userProfile,
        token: tokenResult.token
      };

    } catch (error) {
      logger.error({ err: error }, 'Error during user login');
      return { success: false, error: 'Internal server error during authentication' };
    }
  }

  /**
   * Generate user JWT token
   */
  generateToken(payload: { userId: string; email: string }): { success: boolean; token?: string; error?: string } {
    try {
      const tokenPayload = { userId: payload.userId, email: payload.email };
      const options: jwt.SignOptions = {
        expiresIn: USER_JWT_EXPIRES_IN as any
      };
      const token = jwt.sign(tokenPayload, USER_JWT_SECRET as jwt.Secret, options);
      return { success: true, token };
    } catch (error) {
      logger.error({ err: error }, 'Failed to generate user JWT token');
      return { success: false, error: 'Failed to generate authentication token' };
    }
  }

  /**
   * Verify user JWT token
   */
  verifyToken(token: string): { success: boolean; payload?: UserTokenPayload; error?: string } {
    try {
      const decoded = jwt.verify(token, USER_JWT_SECRET) as UserTokenPayload;
      return { success: true, payload: decoded };
    } catch (error) {
      logger.warn({ err: error }, 'User JWT verification failed');
      return { success: false, error: 'Invalid token' };
    }
  }

  /**
   * Check if user can process emails (monthly allowance + credits)
   */
  async checkUserUsageLimit(userId: string): Promise<boolean> {
    try {
      return await UsageCalculationService.canProcessEmails(userId, 1);
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to check user usage limit');
      return false;
    }
  }

  /**
   * Process email usage (monthly allowance first, then credits)
   */
  async incrementUserEmailUsage(userId: string): Promise<void> {
    try {
      const result = await UsageCalculationService.processEmailUsage(userId, 1);
      if (!result.success) {
        logger.error({
          userId,
          error: result.error
        }, 'Failed to process email usage');
        throw new Error(result.error || 'Failed to process email usage');
      }

      logger.debug({
        userId,
        source: result.source,
        remainingMonthly: result.remainingMonthly,
        remainingCredits: result.remainingCredits
      }, 'Email usage processed');
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to increment user email usage');
      throw error;
    }
  }

  /**
   * Update user's plan and adjust limits accordingly
   */
  async updateUserPlan(userId: string, newPlanType: string): Promise<{ success: boolean; error?: string; user?: UserProfile }> {
    try {
      // Validate plan type
      const planConfig = PlanConfigService.getPlanConfig(newPlanType);

      // Get current user data to validate the change
      const currentUser = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          planType: true,
          _count: {
            select: {
              domains: true,
              webhooks: true
            }
          }
        }
      });

      if (!currentUser) {
        return { success: false, error: 'User not found' };
      }

      // Get alias count
      const aliasCount = await prisma.alias.count({
        where: { domain: { userId } }
      });

      // Validate usage against new plan limits
      const usageValidation = PlanConfigService.validateUsageForPlan(newPlanType, {
        domains: currentUser._count.domains,
        webhooks: currentUser._count.webhooks,
        aliases: aliasCount
      });

      if (!usageValidation.valid) {
        return {
          success: false,
          error: `Cannot change to ${newPlanType} plan: ${usageValidation.violations.join(', ')}`
        };
      }

      // Update user plan
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          planType: newPlanType
        },
        select: {
          id: true,
          email: true,
          name: true,
          planType: true,
          currentMonthEmails: true,
          verified: true
        }
      });

      // Sync plan changes to postfix-manager for all user domains
      try {
        const { PostfixManager } = await import('../postfix-manager.js');
        const postfixManager = new PostfixManager();

        await postfixManager.updateUserDomainsPlan(userId, newPlanType);
      } catch (error: any) {
        // Log error but don't fail the plan update
        console.error(`Failed to sync plan change to postfix-manager for user ${userId}:`, error.message);
      }

      logger.info({ userId, oldPlan: currentUser.planType, newPlan: newPlanType }, 'User plan updated');

      return { success: true, user: updatedUser };
    } catch (error: any) {
      logger.error({ error: error.message, userId, newPlanType }, 'Failed to update user plan');
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user's current plan information with enhanced usage data
   */
  async getUserPlanInfo(userId: string): Promise<{ planConfig: any; usage: any; limits: any } | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { planType: true }
      });

      if (!user) return null;

      const planConfig = PlanConfigService.getPlanConfig(user.planType);
      const usageInfo = await UsageCalculationService.getUserUsageInfo(userId);

      return {
        planConfig,
        usage: usageInfo.currentUsage,
        limits: usageInfo.limits
      };
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to get user plan info');
      return null;
    }
  }

  /**
   * Get cookie configuration for user token
   */
  getCookieConfig() {
    return {
      path: '/',
      httpOnly: true,
      secure: env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
    };
  }
}
