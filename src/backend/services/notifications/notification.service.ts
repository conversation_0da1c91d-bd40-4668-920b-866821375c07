import { logger } from '../../utils/logger.js';
import { prisma } from '../../lib/prisma.js';
import { webSocketService } from '../websocket.service.js';

export interface NotificationData {
  userId: string;
  type: 'PLAN_LIMIT_REACHED' | 'PLAN_LIMIT_WARNING' | 'EMAIL_QUOTA_EXHAUSTED' | 'SYSTEM_ALERT';
  title: string;
  message: string;
  category: 'BILLING' | 'SYSTEM' | 'SECURITY' | 'FEATURE';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  data?: Record<string, any>;
  actionUrl?: string;
  actionText?: string;
}

/**
 * Service for managing user notifications
 */
export class NotificationService {
  
  /**
   * Create a new notification
   */
  static async createNotification(notificationData: NotificationData): Promise<void> {
    try {
      // Debug logging to check if prisma is available
      if (!prisma) {
        logger.error('Prisma client is undefined in notification service');
        return; // Fail gracefully instead of throwing
      }
      
      if (!prisma.notification) {
        logger.error('Prisma notification model is undefined');
        return; // Fail gracefully instead of throwing
      }
      const notification = await prisma.notification.create({
        data: {
          userId: notificationData.userId,
          type: notificationData.type,
          title: notificationData.title,
          message: notificationData.message,
          category: notificationData.category,
          priority: notificationData.priority,
          data: notificationData.data,
          actionUrl: notificationData.actionUrl,
          actionText: notificationData.actionText,
          isRead: false
        }
      });

      logger.info({
        userId: notificationData.userId,
        type: notificationData.type,
        category: notificationData.category,
        priority: notificationData.priority
      }, 'Notification created');

      // Emit WebSocket event for real-time notification
      webSocketService.emitNotificationCreated(notificationData.userId, {
        id: notification.id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        category: notification.category,
        priority: notification.priority,
        actionUrl: notification.actionUrl || undefined,
        actionText: notification.actionText || undefined,
        timestamp: notification.createdAt.toISOString()
      });

    } catch (error) {
      logger.error({
        userId: notificationData.userId,
        type: notificationData.type,
        error: error instanceof Error ? error.message : error
      }, 'Failed to create notification');
      throw error;
    }
  }

  /**
   * Get notifications for a user
   */
  static async getUserNotifications(
    userId: string,
    options: {
      unreadOnly?: boolean;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<any[]> {
    try {
      // Check if prisma and notification model are available
      if (!prisma || !prisma.notification) {
        logger.warn({ userId }, 'Prisma notification model not available, returning empty array');
        return [];
      }

      const { unreadOnly = false, limit = 20, offset = 0 } = options;
      
      const notifications = await prisma.notification.findMany({
        where: {
          userId,
          ...(unreadOnly && { isRead: false })
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: limit,
        skip: offset
      });

      return notifications;
      
    } catch (error) {
      logger.error({ userId, error }, 'Failed to get user notifications');
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId: string, userId: string): Promise<void> {
    try {
      await prisma.notification.updateMany({
        where: {
          id: notificationId,
          userId // Ensure user can only mark their own notifications
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      });

      logger.info({ notificationId, userId }, 'Notification marked as read');
      
    } catch (error) {
      logger.error({ notificationId, userId, error }, 'Failed to mark notification as read');
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  static async markAllAsRead(userId: string): Promise<void> {
    try {
      await prisma.notification.updateMany({
        where: {
          userId,
          isRead: false
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      });

      logger.info({ userId }, 'All notifications marked as read');
      
    } catch (error) {
      logger.error({ userId, error }, 'Failed to mark all notifications as read');
      throw error;
    }
  }

  /**
   * Get unread notification count for a user
   */
  static async getUnreadCount(userId: string): Promise<number> {
    try {
      // Check if prisma and notification model are available
      if (!prisma || !prisma.notification) {
        logger.warn({ userId }, 'Prisma notification model not available, returning 0');
        return 0;
      }

      const count = await prisma.notification.count({
        where: {
          userId,
          isRead: false
        }
      });

      return count;
      
    } catch (error) {
      logger.error({ userId, error }, 'Failed to get unread notification count');
      // Return 0 instead of throwing to prevent breaking the UI
      return 0;
    }
  }

  /**
   * Delete old notifications (cleanup)
   */
  static async deleteOldNotifications(olderThanDays: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);
      
      const result = await prisma.notification.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          },
          isRead: true // Only delete read notifications
        }
      });

      logger.info({ 
        olderThanDays, 
        cutoffDate,
        deletedCount: result.count 
      }, 'Old notifications deleted');
      
    } catch (error) {
      logger.error({ olderThanDays, error }, 'Failed to delete old notifications');
      throw error;
    }
  }

  /**
   * Create plan limit reached notification
   */
  static async createPlanLimitNotification(
    userId: string,
    resourceType: string,
    currentUsage: number,
    limit: number
  ): Promise<void> {
    await this.createNotification({
      userId,
      type: 'PLAN_LIMIT_REACHED',
      title: `${resourceType.charAt(0).toUpperCase() + resourceType.slice(1)} limit reached`,
      message: `You've reached your plan limit of ${limit} ${resourceType}. Upgrade to add more.`,
      category: 'BILLING',
      priority: 'HIGH',
      data: {
        resourceType,
        currentUsage,
        limit,
        upgradeUrl: '/settings#billing'
      },
      actionUrl: '/settings#billing',
      actionText: 'Upgrade Plan'
    });
  }

  /**
   * Create plan limit warning notification
   */
  static async createPlanWarningNotification(
    userId: string,
    resourceType: string,
    currentUsage: number,
    limit: number,
    threshold: string
  ): Promise<void> {
    await this.createNotification({
      userId,
      type: 'PLAN_LIMIT_WARNING',
      title: `${resourceType.charAt(0).toUpperCase() + resourceType.slice(1)} limit warning`,
      message: `You're using ${currentUsage}/${limit} ${resourceType} (${threshold} of your limit). Consider upgrading soon.`,
      category: 'BILLING',
      priority: threshold === '90%' ? 'HIGH' : 'MEDIUM',
      data: {
        resourceType,
        currentUsage,
        limit,
        threshold,
        upgradeUrl: '/settings#billing'
      },
      actionUrl: '/settings#billing',
      actionText: 'View Plans'
    });
  }

  /**
   * Create email quota exhausted notification
   */
  static async createEmailQuotaNotification(userId: string): Promise<void> {
    await this.createNotification({
      userId,
      type: 'EMAIL_QUOTA_EXHAUSTED',
      title: 'Email quota exhausted',
      message: 'You have no remaining email quota. Purchase credits or upgrade your plan to continue.',
      category: 'BILLING',
      priority: 'HIGH',
      data: {
        upgradeUrl: '/settings#billing'
      },
      actionUrl: '/settings#billing',
      actionText: 'Get More Emails'
    });
  }
}
