import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useApi } from './useApi'

export interface Notification {
  id: string
  type: 'PLAN_LIMIT_REACHED' | 'PLAN_LIMIT_WARNING' | 'EMAIL_QUOTA_EXHAUSTED' | 'SYSTEM_ALERT' | 'FEATURE_ANNOUNCEMENT' | 'SECURITY_ALERT' | 'PAYMENT_REMINDER' | 'PAYMENT_FAILED' | 'PAYMENT_SUCCESS' | 'DOMAIN_VERIFIED' | 'DOMAIN_FAILED' | 'WEBHOOK_FAILED'
  title: string
  message: string
  category: 'BILLING' | 'SYSTEM' | 'SECURITY' | 'FEATURE' | 'DOMAIN' | 'WEBHOOK' | 'PAYMENT'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  data?: Record<string, any>
  actionUrl?: string
  actionText?: string
  isRead: boolean
  readAt?: string
  createdAt: string
  expiresAt?: string
}

interface NotificationsResponse {
  success: boolean
  notifications: Notification[]
  total: number
  unreadCount: number
}

interface UnreadCountResponse {
  success: boolean
  unreadCount: number
}

// Global notifications state
const notifications = ref<Notification[]>([])
const unreadCount = ref<number>(0)
const loading = ref<boolean>(false)
const error = ref<string | null>(null)

// Polling interval (30 seconds)
let pollInterval: number | null = null
const POLL_INTERVAL = 30000

export function useNotifications() {
  const { get, patch } = useApi()

  // Computed properties
  const hasUnread = computed(() => unreadCount.value > 0)
  const urgentNotifications = computed(() => 
    notifications.value.filter(n => !n.isRead && n.priority === 'URGENT')
  )

  /**
   * Fetch notifications from the API
   */
  const fetchNotifications = async (options: {
    unreadOnly?: boolean
    limit?: number
    offset?: number
  } = {}): Promise<void> => {
    try {
      loading.value = true
      error.value = null

      const params = new URLSearchParams()
      if (options.unreadOnly) params.append('unreadOnly', 'true')
      if (options.limit) params.append('limit', options.limit.toString())
      if (options.offset) params.append('offset', options.offset.toString())

      const response = await get<NotificationsResponse>(`/api/notifications?${params.toString()}`)
      
      if (response.success) {
        notifications.value = response.notifications
        unreadCount.value = response.unreadCount
      } else {
        throw new Error('Failed to fetch notifications')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch notifications'
      console.error('Error fetching notifications:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * Fetch only the unread count (lightweight)
   */
  const fetchUnreadCount = async (): Promise<void> => {
    try {
      const response = await get<UnreadCountResponse>('/api/notifications/unread-count')
      
      if (response.success) {
        unreadCount.value = response.unreadCount
      }
    } catch (err) {
      // Only log authentication errors, ignore others for background polling
      if (err instanceof Error && (err.message.includes('401') || err.message.includes('403'))) {
        // User not authenticated yet, skip silently
        return
      }
      console.warn('Error fetching unread count:', err)
    }
  }

  /**
   * Mark a notification as read
   */
  const markAsRead = async (notificationId: string): Promise<void> => {
    try {
      const response = await patch(`/api/notifications/${notificationId}/read`) as { success: boolean }
      
      if (response.success) {
        // Update local state
        const notification = notifications.value.find(n => n.id === notificationId)
        if (notification && !notification.isRead) {
          notification.isRead = true
          notification.readAt = new Date().toISOString()
          unreadCount.value = Math.max(0, unreadCount.value - 1)
        }
      }
    } catch (err) {
      console.error('Error marking notification as read:', err)
      throw err
    }
  }

  /**
   * Mark all notifications as read
   */
  const markAllAsRead = async (): Promise<void> => {
    try {
      const response = await patch('/api/notifications/mark-all-read') as { success: boolean }     
      
      if (response.success) {
        // Update local state
        notifications.value.forEach(notification => {
          if (!notification.isRead) {
            notification.isRead = true
            notification.readAt = new Date().toISOString()
          }
        })
        unreadCount.value = 0
      }
    } catch (err) {
      console.error('Error marking all notifications as read:', err)
      throw err
    }
  }

  /**
   * Get the icon for a notification type
   */
  const getNotificationIcon = (notification: Notification): string => {
    switch (notification.type) {
      case 'PLAN_LIMIT_REACHED':
      case 'PLAN_LIMIT_WARNING':
        return 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
      case 'EMAIL_QUOTA_EXHAUSTED':
        return 'M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'
      case 'PAYMENT_SUCCESS':
        return 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
      case 'PAYMENT_FAILED':
        return 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
      case 'DOMAIN_VERIFIED':
        return 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0121 12c0 6.627-5.373 12-12 12S-3 18.627-3 12s5.373-12 12-12c2.24 0 4.368.616 6.177 1.69'
      case 'WEBHOOK_FAILED':
        return 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
      case 'SECURITY_ALERT':
        return 'M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
      default:
        return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
    }
  }

  /**
   * Get the color class for a notification priority
   */
  const getPriorityColorClass = (priority: Notification['priority']): string => {
    switch (priority) {
      case 'URGENT':
        return 'text-error'
      case 'HIGH':
        return 'text-warning'
      case 'MEDIUM':
        return 'text-info'
      case 'LOW':
      default:
        return 'text-base-content'
    }
  }

  /**
   * Format relative time
   */
  const formatRelativeTime = (dateString: string): string => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return 'Just now'
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return `${minutes}m ago`
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600)
      return `${hours}h ago`
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400)
      return `${days}d ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  /**
   * Start polling for new notifications
   */
  const startPolling = (): void => {
    if (pollInterval) return // Already polling

    pollInterval = window.setInterval(() => {
      fetchUnreadCount()
    }, POLL_INTERVAL)
  }

  /**
   * Stop polling for notifications
   */
  const stopPolling = (): void => {
    if (pollInterval) {
      clearInterval(pollInterval)
      pollInterval = null
    }
  }

  /**
   * Initialize notifications (fetch and start polling)
   */
  const initializeNotifications = async (): Promise<void> => {
    await fetchUnreadCount()
    startPolling()
  }

  // Cleanup on unmount
  onUnmounted(() => {
    stopPolling()
  })

  return {
    // State
    notifications: computed(() => notifications.value),
    unreadCount: computed(() => unreadCount.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    hasUnread,
    urgentNotifications,

    // Actions
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    initializeNotifications,
    startPolling,
    stopPolling,

    // Utilities
    getNotificationIcon,
    getPriorityColorClass,
    formatRelativeTime
  }
}
