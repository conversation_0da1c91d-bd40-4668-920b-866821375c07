<template>
  <div class="px-6 py-8 mx-auto max-w-7xl lg:px-8">
    <div class="mb-8">
      <h1 class="text-2xl font-bold">Settings</h1>
      <p class="mt-2">Manage your account settings and preferences.</p>
    </div>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <!-- Settings Navigation -->
      <div class="lg:col-span-1">
        <nav class="space-y-1">
          <a
            href="#profile"
            @click.prevent="setActiveSection('profile')"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'profile'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Profile
          </a>
          <a
            href="#api-keys"
            @click.prevent="setActiveSection('api-keys')"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'api-keys'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10zM12 11v2m0 4h.01" />
            </svg>
            API keys
          </a>
          <a
            href="#storage"
            @click.prevent="setActiveSection('storage')"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'storage'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z" />
            </svg>
            Storage
          </a>
          <a
            href="#retention"
            @click.prevent="setActiveSection('retention')"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'retention'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Data retention
          </a>
          <a
            href="#billing"
            @click.prevent="setActiveSection('billing')"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'billing'
                ? 'text-primary border-r-2 border-primary bg-primary/10'
                : 'text-base-content/70 hover:text-base-content hover:bg-base-200'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            Billing
          </a>
        </nav>
      </div>

      <!-- Settings Content -->
      <div class="lg:col-span-2">

        <!-- Profile Section -->
        <ProfileSection v-if="activeSection === 'profile'" />

        <!-- API Keys Section -->
        <ApiKeysSection v-if="activeSection === 'api-keys'" />

        <!-- Billing Section -->
        <BillingSection v-if="activeSection === 'billing'" />

        <!-- Storage Section -->
        <StorageSection v-if="activeSection === 'storage'" />

        <!-- Retention Section -->
        <RetentionSection v-if="activeSection === 'retention'" />
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BillingSection from './BillingSection.vue'
import ApiKeysSection from './ApiKeysSection.vue'
import StorageSection from './StorageSection.vue'
import RetentionSection from './RetentionSection.vue'
import ProfileSection from './ProfileSection.vue'
import { useMetrics } from '../../composables/useMetrics'

// Router
const route = useRoute()
const router = useRouter()

// State
const activeSection = ref('profile')
const user = ref(null)

// Use metrics to get user data
const { metricsData, loadMetrics } = useMetrics()

// Computed
const currentUser = computed(() => {
  return user.value || metricsData.value?.user || null
})

// Navigation methods
const setActiveSection = (section: string) => {
  activeSection.value = section
  // Update URL hash without triggering navigation
  router.replace({ hash: `#${section}` })
}

const initializeFromHash = () => {
  const hash = route.hash.replace('#', '')
  const validSections = ['profile', 'api-keys', 'storage', 'retention', 'billing']

  if (hash && validSections.includes(hash)) {
    activeSection.value = hash
  } else {
    activeSection.value = 'profile'
  }
}

// Methods
const loadUserProfile = async () => {
  try {
    await loadMetrics()
    if (metricsData.value?.user) {
      user.value = metricsData.value.user
    }
  } catch (error) {
    console.error('Failed to load user profile:', error)
  }
}

// Watch for route hash changes
watch(() => route.hash, () => {
  initializeFromHash()
}, { immediate: false })

watch(currentUser, (newUser) => {
  if (newUser) {
  }
}, { immediate: true })

onMounted(() => {
  // Initialize section from URL hash first
  initializeFromHash()
  // Then load user profile
  loadUserProfile()
})
</script>

<style scoped>
/* Settings page specific styles */
</style>
